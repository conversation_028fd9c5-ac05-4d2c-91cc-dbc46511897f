# frozen_string_literal: true

class FacultyTimeSlotsController < ApplicationController
  before_action :require_user
  before_action :require_faculty_access
  before_action :set_time_slot, only: [:show, :update, :destroy]

  # GET /faculty_time_slots
  def index
    @time_slots = @current_user.faculty_time_slots.order(:day_of_week, :start_time)

    respond_to do |format|
      format.json { render json: time_slots_json(@time_slots) }
      format.html { render_faculty_time_slots_page }
    end
  end

  # GET /faculty_time_slots/:id
  def show
    respond_to do |format|
      format.json { render json: time_slot_json(@time_slot) }
    end
  end

  # POST /faculty_time_slots
  def create
    @time_slot = @current_user.faculty_time_slots.build(time_slot_params)

    if @time_slot.save
      respond_to do |format|
        format.json { render json: time_slot_json(@time_slot), status: :created }
      end
    else
      respond_to do |format|
        format.json { render json: { errors: @time_slot.errors.full_messages }, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /faculty_time_slots/:id
  def update
    if @time_slot.update(time_slot_params)
      respond_to do |format|
        format.json { render json: time_slot_json(@time_slot) }
      end
    else
      respond_to do |format|
        format.json { render json: { errors: @time_slot.errors.full_messages }, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /faculty_time_slots/:id
  def destroy
    if @time_slot.consultation_requests.pending.exists?
      respond_to do |format|
        format.json { 
          render json: { 
            error: 'Cannot delete time slot with pending consultation requests' 
          }, status: :unprocessable_entity 
        }
      end
    else
      @time_slot.destroy
      respond_to do |format|
        format.json { head :no_content }
      end
    end
  end

  # GET /faculty_time_slots/available_dates
  def available_dates
    start_date = Date.parse(params[:start_date]) rescue Date.current
    end_date = Date.parse(params[:end_date]) rescue (start_date + 30.days)

    # Use optimized method to avoid N+1 queries
    available_dates = FacultyTimeSlot.available_dates_for_faculty_in_range(@current_user, start_date, end_date)

    respond_to do |format|
      format.json { render json: { available_dates: available_dates } }
    end
  end

  # GET /faculty_time_slots/available_times
  def available_times
    date = Date.parse(params[:date]) rescue Date.current
    slots = FacultyTimeSlot.available_for_faculty_on_date(@current_user, date)
    
    available_times = []
    slots.each do |slot|
      slot.available_datetimes_for_date(date).each do |datetime|
        available_times << {
          datetime: datetime.iso8601,
          formatted_time: datetime.strftime('%I:%M %p'),
          slot_id: slot.id
        }
      end
    end

    respond_to do |format|
      format.json { render json: { available_times: available_times.sort_by { |t| t[:datetime] } } }
    end
  end

  private

  def require_faculty_access
    unless @current_user.teacher_enrollments.active.exists?
      respond_to do |format|
        format.json { render json: { error: 'Access denied. Faculty access required.' }, status: :forbidden }
        format.html {
          flash[:error] = 'Access denied. Faculty access required.'
          redirect_to root_path
        }
      end
    end
  end

  def set_time_slot
    @time_slot = @current_user.faculty_time_slots.find(params[:id])
  rescue ActiveRecord::RecordNotFound
    respond_to do |format|
      format.json { render json: { error: 'Time slot not found' }, status: :not_found }
      format.html {
        flash[:error] = 'Time slot not found'
        redirect_to faculty_time_slots_path
      }
    end
  end

  def time_slot_params
    params.require(:faculty_time_slot).permit(
      :start_time, :end_time, :day_of_week, :is_recurring, 
      :specific_date, :is_available, :notes
    )
  end

  def time_slot_json(time_slot)
    {
      id: time_slot.id,
      start_time: time_slot.start_time.strftime('%H:%M'),
      end_time: time_slot.end_time.strftime('%H:%M'),
      day_of_week: time_slot.day_of_week,
      is_recurring: time_slot.is_recurring,
      specific_date: time_slot.specific_date&.iso8601,
      is_available: time_slot.is_available,
      notes: time_slot.notes,
      created_at: time_slot.created_at.iso8601,
      updated_at: time_slot.updated_at.iso8601,
      pending_requests_count: time_slot.consultation_requests.pending.count
    }
  end

  def time_slots_json(time_slots)
    {
      time_slots: time_slots.map { |slot| time_slot_json(slot) },
      total_count: time_slots.count,
      available_count: time_slots.available.count
    }
  end

  def render_faculty_time_slots_page
    @page_title = 'Manage Consultation Time Slots'
    js_env({
      FACULTY_TIME_SLOTS: {
        current_user_id: @current_user.id,
        time_slots: time_slots_json(@time_slots)[:time_slots],
        days_of_week: FacultyTimeSlot::DAYS_OF_WEEK
      }
    })

    js_bundle :faculty_time_slots
    css_bundle :consultation_system
  end
end
