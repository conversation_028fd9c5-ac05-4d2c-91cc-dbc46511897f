import React, { useState } from 'react'
import { View } from '@instructure/ui-view'
import { Heading } from '@instructure/ui-heading'
import { Button } from '@instructure/ui-buttons'
import { TextInput } from '@instructure/ui-text-input'
import { Select } from '@instructure/ui-select'
import { TextArea } from '@instructure/ui-text-area'
import { FormFieldGroup } from '@instructure/ui-form-field'
import { Text } from '@instructure/ui-text'
import { Checkbox } from '@instructure/ui-checkbox'
import type {
  StudentInfo,
  FacultyUser,
  ConsultationRequestFormData,
  AvailableDate,
  AvailableDateTime,
  FormErrors
} from '../types'
import { CONCERN_TYPES } from '../types'

interface ConsultationRequestFormProps {
  studentInfo: StudentInfo
  availableFaculty: FacultyUser[]
  availableDates: AvailableDate[]
  availableTimes: AvailableDateTime[]
  selectedFaculty: string
  selectedDate: string
  onFacultyChange: (facultyId: string) => void
  onDateChange: (date: string) => void
  onSubmit: (data: ConsultationRequestFormData) => Promise<void>
  loading: boolean
  concernTypes: string[]
}

const ConsultationRequestForm: React.FC<ConsultationRequestFormProps> = ({
  studentInfo,
  availableFaculty,
  availableDates,
  availableTimes,
  selectedFaculty,
  selectedDate,
  onFacultyChange,
  onDateChange,
  onSubmit,
  loading,
  concernTypes
}) => {
  const [formData, setFormData] = useState<ConsultationRequestFormData>({
    faculty_time_slot_id: '',
    preferred_datetime: '',
    description: '',
    nature_of_concern: '',
    custom_concern: '',
    college_campus_institute: '',
    department_program: '',
    semester: '',
    academic_year: '',
    place_of_consultation: '',
    intervention_given: '',
    referral_made: '',
    students_adviser_agreement: false,
    prepared_by_name: '',
    prepared_by_designation: '',
    noted_by_program_chair: '',
    noted_by_college_dean: '',
    conformance_signature: ''
  })

  const [errors, setErrors] = useState<FormErrors>({})
  const [facultySearchValue, setFacultySearchValue] = useState('')
  const [isShowingFacultyOptions, setIsShowingFacultyOptions] = useState(false)

  // Use nature of concern options from props (passed from backend)
  const natureOfConcernOptions = concernTypes.length > 0 ? concernTypes : CONCERN_TYPES

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {}

    if (!selectedFaculty) {
      newErrors.faculty = 'Please select a faculty member'
    }

    if (!selectedDate) {
      newErrors.date = 'Please select a date'
    }

    if (!formData.preferred_datetime) {
      newErrors.preferred_datetime = 'Please select a time'
    }

    if (!formData.nature_of_concern) {
      newErrors.nature_of_concern = 'Please select the nature of your concern'
    }

    if (formData.nature_of_concern === 'Others' && !formData.custom_concern?.trim()) {
      newErrors.custom_concern = 'Please specify the nature of your concern'
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Please provide a description of your concern'
    } else if (formData.description.trim().length < 20) {
      newErrors.description = 'Please provide a more detailed description (at least 20 characters)'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    try {
      await onSubmit(formData)
      // Reset form on success
      setFormData({
        faculty_time_slot_id: '',
        preferred_datetime: '',
        description: '',
        nature_of_concern: '',
        custom_concern: '',
        college_campus_institute: '',
        department_program: '',
        semester: '',
        academic_year: '',
        place_of_consultation: '',
        intervention_given: '',
        referral_made: '',
        students_adviser_agreement: false,
        prepared_by_name: '',
        prepared_by_designation: '',
        noted_by_program_chair: '',
        noted_by_college_dean: '',
        conformance_signature: ''
      })
    } catch (error) {
      // Error handling is done in parent component
    }
  }

  const handleInputChange = (field: keyof ConsultationRequestFormData, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: field === 'students_adviser_agreement' ? (value === 'true' || value === true) : value
    }))

    // Clear custom concern when nature of concern changes to something other than "Others"
    if (field === 'nature_of_concern' && value !== 'Others') {
      setFormData(prev => ({
        ...prev,
        custom_concern: ''
      }))
    }

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }))
    }
  }

  const handleTimeChange = (datetime: string, slotId: string) => {
    setFormData(prev => ({
      ...prev,
      preferred_datetime: datetime,
      faculty_time_slot_id: slotId
    }))

    if (errors.preferred_datetime) {
      setErrors(prev => ({
        ...prev,
        preferred_datetime: ''
      }))
    }
  }

  // Faculty search handlers
  const handleFacultyInputChange = (value: string) => {
    setFacultySearchValue(value)
  }

  const handleShowFacultyOptions = () => {
    setIsShowingFacultyOptions(true)
  }

  const handleHideFacultyOptions = () => {
    setIsShowingFacultyOptions(false)
  }

  const handleSelectFaculty = (facultyId: string) => {
    const selectedFacultyMember = availableFaculty.find(f => f.id === facultyId)
    if (selectedFacultyMember) {
      setFacultySearchValue(selectedFacultyMember.name)
      onFacultyChange(facultyId)
    }
    setIsShowingFacultyOptions(false)
  }

  // Filter faculty based on search input
  const filteredFaculty = availableFaculty.filter(faculty =>
    faculty.name.toLowerCase().includes(facultySearchValue.toLowerCase()) ||
    (faculty.department && faculty.department.toLowerCase().includes(facultySearchValue.toLowerCase()))
  )

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString)
      return date.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    } catch {
      return dateString
    }
  }

  return (
    <View as="div" background="primary" padding="medium" borderRadius="medium">
      <Heading level="h2" margin="0 0 medium 0">
        Consultation Request Form
      </Heading>

      <form onSubmit={handleSubmit}>
        <FormFieldGroup description="Request Details" layout="stacked">
          <Select
            renderLabel="Select Faculty Member"
            placeholder="Type to search or click to see all faculty members..."
            inputValue={facultySearchValue}
            isShowingOptions={isShowingFacultyOptions}
            onInputChange={handleFacultyInputChange}
            onRequestShowOptions={handleShowFacultyOptions}
            onRequestHideOptions={handleHideFacultyOptions}
            onRequestSelectOption={(e, { id }) => handleSelectFaculty(id as string)}
            messages={errors.faculty ? [{ text: errors.faculty, type: 'error' }] : []}
            assistiveText="Type to search by faculty name or department, or click to see all options"
          >
            {filteredFaculty.map(faculty => (
              <Select.Option key={faculty.id} id={faculty.id} value={faculty.id}>
                {faculty.name} {faculty.department && `(${faculty.department})`}
                <Text size="small" color="secondary" display="block">
                  {faculty.available_slots_count} available time slot{faculty.available_slots_count !== 1 ? 's' : ''}
                </Text>
              </Select.Option>
            ))}
          </Select>

          {selectedFaculty && availableDates.length > 0 && (
            <Select
              renderLabel="Select Date"
              placeholder="Choose a date..."
              value={selectedDate}
              onChange={(e, { value }) => onDateChange(value as string)}
              messages={errors.date ? [{ text: errors.date, type: 'error' }] : []}
            >
              {availableDates.map(date => (
                <Select.Option key={date.date} id={date.date} value={date.date}>
                  {formatDate(date.date)}
                  <Text size="small" color="secondary" display="block">
                    {date.slots_count} available time slot{date.slots_count !== 1 ? 's' : ''}
                  </Text>
                </Select.Option>
              ))}
            </Select>
          )}

          {selectedDate && availableTimes.length > 0 && (
            <Select
              renderLabel="Select Time"
              placeholder="Choose a time..."
              value={formData.preferred_datetime}
              onChange={(e, { value }) => {
                const selectedTime = availableTimes.find(time => time.datetime === value)
                if (selectedTime) {
                  handleTimeChange(selectedTime.datetime, selectedTime.slot_id)
                }
              }}
              messages={errors.preferred_datetime ? [{ text: errors.preferred_datetime, type: 'error' }] : []}
            >
              {availableTimes.map(time => (
                <Select.Option key={time.datetime} id={time.datetime} value={time.datetime}>
                  {time.formatted_time}
                </Select.Option>
              ))}
            </Select>
          )}

          <Select
            renderLabel="Nature of Concern"
            placeholder="Select the type of concern..."
            value={formData.nature_of_concern}
            onChange={(e, { value }) => handleInputChange('nature_of_concern', value as string)}
            messages={errors.nature_of_concern ? [{ text: errors.nature_of_concern, type: 'error' }] : []}
          >
            {natureOfConcernOptions.map(type => (
              <Select.Option key={type} id={type} value={type}>
                {type}
              </Select.Option>
            ))}
          </Select>

          {formData.nature_of_concern === 'Others' && (
            <TextArea
              label="Please specify your concern"
              placeholder="Please provide details about your specific concern..."
              value={formData.custom_concern || ''}
              onChange={(e) => handleInputChange('custom_concern', e.target.value)}
              height="4rem"
              messages={errors.custom_concern ? [{ text: errors.custom_concern, type: 'error' }] : []}
            />
          )}

          <TextArea
            label="Description of Concern"
            placeholder="Please provide a detailed description of what you would like to discuss during the consultation. This helps the faculty member prepare for your session."
            value={formData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            height="8rem"
            messages={errors.description ? [{ text: errors.description, type: 'error' }] : []}
          />

          <View as="div" background="secondary" padding="small" borderRadius="small">
            <Text size="small" color="secondary">
              <strong>Note:</strong> Please provide as much detail as possible about your concern.
              This information will help the faculty member prepare for your consultation and ensure
              you get the most out of your session.
            </Text>
          </View>
        </FormFieldGroup>

        <FormFieldGroup description="Additional Information" layout="stacked">
          <TextInput
            renderLabel="College/Campus/Institute"
            placeholder="Enter your college, campus, or institute"
            value={formData.college_campus_institute || ''}
            onChange={(e) => handleInputChange('college_campus_institute', e.target.value)}
          />

          <TextInput
            renderLabel="Department/Program"
            placeholder="Enter your department or program"
            value={formData.department_program || ''}
            onChange={(e) => handleInputChange('department_program', e.target.value)}
          />

          <View as="div" display="flex" gap="medium">
            <TextInput
              renderLabel="Semester"
              placeholder="e.g., 1st Semester"
              value={formData.semester || ''}
              onChange={(e) => handleInputChange('semester', e.target.value)}
            />

            <TextInput
              renderLabel="Academic Year"
              placeholder="e.g., 2024-2025"
              value={formData.academic_year || ''}
              onChange={(e) => handleInputChange('academic_year', e.target.value)}
            />
          </View>

          <TextInput
            renderLabel="Place of Consultation"
            placeholder="Where will the consultation take place?"
            value={formData.place_of_consultation || ''}
            onChange={(e) => handleInputChange('place_of_consultation', e.target.value)}
          />
        </FormFieldGroup>

        <FormFieldGroup description="Consultation Outcome (To be filled by faculty)" layout="stacked">
          <TextArea
            label="Intervention Given"
            placeholder="Interventions or recommendations provided during consultation (filled by faculty)"
            value={formData.intervention_given || ''}
            onChange={(e) => handleInputChange('intervention_given', e.target.value)}
            height="6rem"
            disabled={true}
          />

          <TextArea
            label="Referral Made"
            placeholder="Any referrals made to other services or professionals (filled by faculty)"
            value={formData.referral_made || ''}
            onChange={(e) => handleInputChange('referral_made', e.target.value)}
            height="4rem"
            disabled={true}
          />

          <Checkbox
            label="Student's Adviser Agreement"
            checked={formData.students_adviser_agreement || false}
            onChange={(e) => handleInputChange('students_adviser_agreement', e.target.checked.toString())}
            disabled={true}
          />

          <View as="div" background="secondary" padding="small" borderRadius="small">
            <Text size="small" color="secondary">
              <strong>Note:</strong> The fields above will be completed by the faculty member during or after your consultation session.
            </Text>
          </View>
        </FormFieldGroup>

        <View as="div" margin="large 0 0 0" display="flex" gap="small" justifyItems="end">
          <Button
            type="submit"
            color="primary"
            disabled={loading || !selectedFaculty || !selectedDate || !formData.preferred_datetime}
          >
            {loading ? 'Submitting Request...' : 'Submit Consultation Request'}
          </Button>
        </View>
      </form>
    </View>
  )
}

export default ConsultationRequestForm
