import React from 'react'
import { View } from '@instructure/ui-view'
import { Heading } from '@instructure/ui-heading'
import { Button } from '@instructure/ui-buttons'
import { Text } from '@instructure/ui-text'
import { Badge } from '@instructure/ui-badge'
import { Grid } from '@instructure/ui-grid'
import { IconUserLine, IconCalendarMonthLine, IconAddLine, IconDocumentLine } from '@instructure/ui-icons'
import type { ConsultationRequest } from '../types'

interface StudentConsultationsProps {
  currentUserId: string
  recentRequests: ConsultationRequest[]
}

const StudentConsultations: React.FC<StudentConsultationsProps> = ({
  currentUserId,
  recentRequests
}) => {
  const formatDateTime = (dateTimeString: string) => {
    try {
      const date = new Date(dateTimeString)
      return date.toLocaleDateString('en-US', {
        weekday: 'short',
        month: 'short',
        day: 'numeric',
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      })
    } catch {
      return dateTimeString
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'warning'
      case 'approved':
        return 'success'
      case 'declined':
        return 'danger'
      case 'completed':
        return 'brand'
      case 'cancelled':
        return 'secondary'
      default:
        return 'secondary'
    }
  }

  const pendingCount = recentRequests.filter(req => req.status === 'pending').length
  const approvedCount = recentRequests.filter(req => req.status === 'approved').length
  const upcomingCount = recentRequests.filter(req => 
    req.status === 'approved' && new Date(req.preferred_datetime) > new Date()
  ).length

  return (
    <div className="consultation-system">
      <View as="div" padding="large">
        <div className="page-header">
          <Heading level="h1" margin="0 0 small 0">
            <IconUserLine /> Student Consultations
          </Heading>
          <p>Manage your consultation requests and view upcoming appointments with faculty members.</p>
        </div>

        {/* Quick Stats */}
        <Grid>
          <Grid.Row>
            <Grid.Col width={3}>
              <View as="div" background="primary" padding="medium" borderRadius="medium" textAlign="center">
                <Text size="xx-large" weight="bold" color="alert">
                  {pendingCount}
                </Text>
                <Text size="small" color="secondary">
                  Pending Requests
                </Text>
              </View>
            </Grid.Col>

            <Grid.Col width={3}>
              <View as="div" background="primary" padding="medium" borderRadius="medium" textAlign="center">
                <Text size="xx-large" weight="bold" color="success">
                  {approvedCount}
                </Text>
                <Text size="small" color="secondary">
                  Approved
                </Text>
              </View>
            </Grid.Col>

            <Grid.Col width={3}>
              <View as="div" background="primary" padding="medium" borderRadius="medium" textAlign="center">
                <Text size="xx-large" weight="bold" color="brand">
                  {upcomingCount}
                </Text>
                <Text size="small" color="secondary">
                  Upcoming
                </Text>
              </View>
            </Grid.Col>

            <Grid.Col width={3}>
              <View as="div" background="primary" padding="medium" borderRadius="medium" textAlign="center">
                <Text size="xx-large" weight="bold" color="brand">
                  {recentRequests.length}
                </Text>
                <Text size="small" color="secondary">
                  Total Requests
                </Text>
              </View>
            </Grid.Col>
          </Grid.Row>
        </Grid>

        {/* Quick Actions */}
        <View as="div" margin="large 0 0 0">
          <Heading level="h2" margin="0 0 medium 0">
            Quick Actions
          </Heading>
          <Grid>
            <Grid.Row>
              <Grid.Col width={6}>
                <View as="div" background="primary" padding="medium" borderRadius="medium" height="100%">
                  <View as="div" display="flex" alignItems="center" margin="0 0 small 0">
                    <IconAddLine size="medium" />
                    <Heading level="h3" margin="0 0 0 small">
                      Request New Consultation
                    </Heading>
                  </View>
                  <Text>
                    Submit a new consultation request with available faculty members. 
                    Get help with academic, personal, or other concerns.
                  </Text>
                  <View as="div" margin="medium 0 0 0">
                    <Button 
                      color="primary" 
                      href="/consultation_requests/student_form"
                      renderIcon={IconAddLine}
                    >
                      New Request
                    </Button>
                  </View>
                </View>
              </Grid.Col>
              
              <Grid.Col width={6}>
                <View as="div" background="primary" padding="medium" borderRadius="medium" height="100%">
                  <View as="div" display="flex" alignItems="center" margin="0 0 small 0">
                    <IconDocumentLine size="medium" />
                    <Heading level="h3" margin="0 0 0 small">
                      View All Requests
                    </Heading>
                  </View>
                  <Text>
                    View and manage all your consultation requests. Track status updates 
                    and upcoming appointments.
                  </Text>
                  <View as="div" margin="medium 0 0 0">
                    <Button 
                      href="/consultation_requests"
                      renderIcon={IconDocumentLine}
                    >
                      View All Requests
                    </Button>
                  </View>
                </View>
              </Grid.Col>
            </Grid.Row>
          </Grid>
        </View>

        {/* Recent Requests */}
        <View as="div" margin="large 0 0 0">
          <Heading level="h2" margin="0 0 medium 0">
            Recent Requests
          </Heading>
          
          {recentRequests.length > 0 ? (
            <View as="div">
              {recentRequests.slice(0, 5).map(request => (
                <View
                  key={request.id}
                  as="div"
                  background="primary"
                  padding="medium"
                  borderRadius="medium"
                  borderWidth="small"
                  borderColor="brand"
                  margin="0 0 small 0"
                >
                  <View as="div" display="flex" justifyItems="space-between" alignItems="center">
                    <View as="div">
                      <View as="div" display="flex" alignItems="center" margin="0 0 x-small 0">
                        <Text weight="bold">
                          {request.faculty_name}
                        </Text>
                        <Badge
                          type={getStatusColor(request.status) as any}
                          text={request.status_display}
                          margin="0 0 0 small"
                        />
                      </View>
                      <View as="div" display="flex" alignItems="center" margin="0 0 x-small 0">
                        <IconCalendarMonthLine size="x-small" />
                        <Text size="small" margin="0 0 0 x-small">
                          {formatDateTime(request.preferred_datetime)}
                        </Text>
                      </View>
                      <Text size="small" color="secondary">
                        {request.concern_type_display}
                      </Text>
                    </View>
                    <View as="div">
                      <Button
                        size="small"
                        href={`/consultation_requests/${request.id}`}
                      >
                        View Details
                      </Button>
                    </View>
                  </View>
                </View>
              ))}
              
              {recentRequests.length > 5 && (
                <View as="div" textAlign="center" margin="medium 0 0 0">
                  <Button href="/consultation_requests">
                    View All {recentRequests.length} Requests
                  </Button>
                </View>
              )}
            </View>
          ) : (
            <View as="div" textAlign="center" padding="large" background="secondary" borderRadius="medium">
              <Text color="secondary">
                You haven't submitted any consultation requests yet. 
                Click "New Request" to get started.
              </Text>
            </View>
          )}
        </View>

        {/* Help Section */}
        <View as="div" margin="large 0 0 0" background="secondary" padding="medium" borderRadius="medium">
          <Heading level="h4" margin="0 0 small 0">
            Need Help?
          </Heading>
          <Text>
            If you have questions about the consultation process or need technical support, 
            please contact your academic advisor or the IT Help Desk.
          </Text>
        </View>
      </View>
    </div>
  )
}

export default StudentConsultations
