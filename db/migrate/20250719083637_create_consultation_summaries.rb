class CreateConsultationSummaries < ActiveRecord::Migration[7.1]
  disable_ddl_transaction!
  tag :predeploy

  def change
    create_table :consultation_summaries do |t|
      t.references :consultation_request, null: false, foreign_key: true, index: true
      t.references :faculty, null: false, foreign_key: { to_table: :users }, index: true
      t.references :student, null: false, foreign_key: { to_table: :users }, index: true
      t.string :student_name, null: false, limit: 255
      t.string :student_number, null: false, limit: 50
      t.datetime :consultation_date, null: false
      t.string :concern_type, null: false, limit: 50
      t.text :description, null: false
      t.text :faculty_notes
      t.text :outcome_summary
      t.text :referral_made
      t.string :follow_up_required, limit: 255
      t.integer :duration_minutes, default: 30

      t.timestamps null: false

      t.index [:faculty_id, :concern_type], name: 'index_consultation_summaries_on_faculty_concern'
      t.index [:student_id, :consultation_date], name: 'index_consultation_summaries_on_student_date'
      t.index [:concern_type, :consultation_date], name: 'index_consultation_summaries_on_concern_date'
    end

    # Add check constraint for consultation_summaries
    add_check_constraint :consultation_summaries,
                         "concern_type IN ('Personal', 'Academic', 'Teacher-related', " \
                         "'Co-students', 'Family', 'Others')",
                         name: 'consultation_summaries_concern_type_check'
  end
end
