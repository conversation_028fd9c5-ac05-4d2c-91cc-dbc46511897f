class CreateFacultyTimeSlots < ActiveRecord::Migration[7.1]
  disable_ddl_transaction!
  tag :predeploy

  def change
    create_table :faculty_time_slots do |t|
      t.references :user, null: false, foreign_key: true, index: true
      t.string :day_of_week, null: false, limit: 10
      t.time :start_time, null: false
      t.time :end_time, null: false
      t.date :specific_date
      t.boolean :is_recurring, default: true, null: false
      t.boolean :is_available, default: true, null: false
      t.text :notes

      t.timestamps null: false

      t.index [:user_id, :day_of_week, :start_time], name: 'index_faculty_time_slots_on_user_day_time'
      t.index [:user_id, :specific_date], name: 'index_faculty_time_slots_on_user_specific_date'
      t.index [:is_available, :start_time], name: 'index_faculty_time_slots_on_availability'
    end

    # Add check constraints for valid enum values
    add_check_constraint :faculty_time_slots,
                         "day_of_week IN ('Monday', 'Tuesday', 'Wednesday', " \
                         "'Thursday', 'Friday', 'Saturday', 'Sunday')",
                         name: 'faculty_time_slots_day_of_week_check'

    # Add time validation constraints
    add_check_constraint :faculty_time_slots,
                         'end_time > start_time',
                         name: 'faculty_time_slots_time_order_check'
  end
end
